package main

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/gif"
	"image/jpeg"
	"image/png"
	"log"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/disintegration/imaging"
	pigo "github.com/esimov/pigo/core"
	"github.com/go-audio/wav"
	"github.com/makiuchi-d/gozxing"
	"github.com/makiuchi-d/gozxing/qrcode"
)

const (
	outputWidth  = 640
	outputHeight = 480
	frameRate    = 24
)

// FaceDetector 人脸检测器
type FaceDetector struct {
	cascadeFile string
	classifier  *pigo.Pigo
}

// NewFaceDetector 创建人脸检测器
func NewFaceDetector(cascadeFile string) (*FaceDetector, error) {
	cascade, err := os.ReadFile(cascadeFile)
	if err != nil {
		return nil, fmt.Errorf("读取级联文件失败: %v", err)
	}

	p := pigo.NewPigo()
	classifier, err := p.Unpack(cascade)
	if err != nil {
		return nil, fmt.Errorf("解析级联文件失败: %v", err)
	}

	return &FaceDetector{
		cascadeFile: cascadeFile,
		classifier:  classifier,
	}, nil
}

// DetectFaces 检测图片中的人脸
func (fd *FaceDetector) DetectFaces(img image.Image) ([]pigo.Detection, error) {
	src := imaging.Clone(img)
	gray := imaging.Grayscale(src)
	pixels := pigo.RgbToGrayscale(gray)
	cols, rows := src.Bounds().Dx(), src.Bounds().Dy()

	cParams := pigo.CascadeParams{
		MinSize:     100,
		MaxSize:     600,
		ShiftFactor: 0.1,
		ScaleFactor: 1.1,
		ImageParams: pigo.ImageParams{
			Pixels: pixels,
			Rows:   rows,
			Cols:   cols,
			Dim:    cols,
		},
	}

	dets := fd.classifier.RunCascade(cParams, 0.0)

	// 应用聚类过滤
	dets = fd.classifier.ClusterDetections(dets, 0.2)

	return dets, nil
}

// LipAnimator 嘴部动画生成器
type LipAnimator struct {
	faceDetector *FaceDetector
}

// NewLipAnimator 创建嘴部动画生成器
func NewLipAnimator(cascadeFile string) (*LipAnimator, error) {
	fd, err := NewFaceDetector(cascadeFile)
	if err != nil {
		return nil, err
	}
	return &LipAnimator{faceDetector: fd}, nil
}

// GenerateAnimation 生成说话动画
func (la *LipAnimator) GenerateAnimation(inputImagePath, audioPath, outputVideoPath string) error {
	// 1. 加载输入图片
	img, err := loadImage(inputImagePath)
	if err != nil {
		return fmt.Errorf("加载图片失败: %v", err)
	}

	// 2. 检测人脸
	dets, err := la.faceDetector.DetectFaces(img)
	if err != nil {
		return fmt.Errorf("人脸检测失败: %v", err)
	}
	if len(dets) == 0 {
		return fmt.Errorf("未检测到人脸")
	}

	// 3. 分析音频
	audioData, err := loadAudioData(audioPath)
	if err != nil {
		return fmt.Errorf("加载音频失败: %v", err)
	}

	// 4. 生成动画帧
	frameDir, err := os.MkdirTemp("", "lip_anim_frames")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(frameDir)

	totalFrames := int(float64(audioData.Duration.Seconds()) * frameRate)
	det := dets[0] // 使用检测到的第一个人脸
	mouthRect := estimateMouthRegion(det, img.Bounds().Dx(), img.Bounds().Dy())

	fmt.Printf("开始生成 %d 帧动画...\n", totalFrames)
	var wg sync.WaitGroup
	framePaths := make([]string, totalFrames)

	for i := 0; i < totalFrames; i++ {
		wg.Add(1)
		go func(frameIndex int) {
			defer wg.Done()
			progress := float64(frameIndex) / float64(totalFrames)
			timeOffset := time.Duration(progress * float64(audioData.Duration))

			// 计算当前帧的嘴部开合程度
			volume := calculateVolumeAtTime(audioData, timeOffset)
			openness := mapVolumeToLipOpenness(volume)

			// 生成动画帧
			frame := generateFrame(img, det, mouthRect, openness)
			framePath := filepath.Join(frameDir, fmt.Sprintf("frame_%04d.png", frameIndex))
			if err := saveImage(framePath, frame); err != nil {
				log.Printf("保存帧失败: %v", err)
				return
			}
			framePaths[frameIndex] = framePath
		}(i)
	}

	wg.Wait()
	fmt.Println("所有动画帧生成完成")

	// 5. 创建视频
	fmt.Println("开始合成视频...")
	if err := createVideoFromFrames(framePaths, audioPath, outputVideoPath, frameRate); err != nil {
		return fmt.Errorf("创建视频失败: %v", err)
	}

	fmt.Printf("动画视频已生成: %s\n", outputVideoPath)
	return nil
}

// generateFrame 生成单个动画帧
func generateFrame(baseImg image.Image, face pigo.Detection, mouthRect image.Rectangle, openness float64) image.Image {
	// 创建新图像
	img := imaging.Clone(baseImg)

	// 调整嘴部区域
	mouthImg := createMouthImage(mouthRect.Dx(), mouthRect.Dy(), openness)

	// 将嘴部图像应用到原图
	img = imaging.Overlay(img, mouthImg, mouthRect.Min, 1.0)

	// 添加二维码水印
	finalImg := addQRCodeWatermark(img, "Generated by Go Lip Animator")

	return finalImg
}

// createMouthImage 创建嘴部图像
func createMouthImage(width, height int, openness float64) image.Image {
	// 创建透明背景
	mouthImg := image.NewRGBA(image.Rect(0, 0, width, height))
	draw.Draw(mouthImg, mouthImg.Bounds(), image.Transparent, image.Point{}, draw.Src)

	// 绘制嘴部
	lipColor := color.RGBA{200, 100, 100, 255}
	lipThickness := 3
	centerY := height / 2

	// 上嘴唇
	upperLipHeight := int(float64(height/3) * openness)
	for y := centerY - upperLipHeight; y < centerY; y++ {
		for x := 0; x < width; x++ {
			// 绘制曲线形状
			if math.Abs(float64(x-width/2)) < float64(width/2)*math.Sin(math.Pi*float64(y-centerY+upperLipHeight)/float64(upperLipHeight)) {
				for t := 0; t < lipThickness; t++ {
					if y+t < centerY {
						mouthImg.Set(x, y+t, lipColor)
					}
				}
			}
		}
	}

	// 下嘴唇
	lowerLipHeight := int(float64(height/4) * openness)
	for y := centerY; y < centerY+lowerLipHeight; y++ {
		for x := 0; x < width; x++ {
			// 绘制曲线形状
			if math.Abs(float64(x-width/2)) < float64(width/2)*math.Sin(math.Pi*float64(y-centerY)/float64(lowerLipHeight)) {
				for t := 0; t < lipThickness; t++ {
					if y-t >= centerY {
						mouthImg.Set(x, y-t, lipColor)
					}
				}
			}
		}
	}

	// 口腔内部 (当嘴张开时)
	if openness > 0.3 {
		mouthOpening := int(float64(height) * openness * 0.8)
		innerMouth := image.Rect(width/4, centerY-mouthOpening/2, 3*width/4, centerY+mouthOpening/2)
		draw.Draw(mouthImg, innerMouth, &image.Uniform{color.RGBA{50, 20, 20, 255}}, image.Point{}, draw.Src)
	}

	return mouthImg
}

// estimateMouthRegion 估计嘴部区域
func estimateMouthRegion(face pigo.Detection, imgWidth, imgHeight int) image.Rectangle {
	// 基于人脸检测结果估计嘴部位置
	// scale := float64(face.Scale) / 100.0
	x := int(float64(face.Col)) - int(float64(face.Scale)/2)
	y := int(float64(face.Row)) - int(float64(face.Scale)/2)

	// 嘴部位于人脸下半部分
	mouthY := y + int(float64(face.Scale)*0.6)
	mouthHeight := int(float64(face.Scale) * 0.2)
	mouthWidth := int(float64(face.Scale) * 0.4)

	return image.Rect(
		x+int(float64(face.Scale)*0.3),
		mouthY,
		x+int(float64(face.Scale)*0.3)+mouthWidth,
		mouthY+mouthHeight,
	)
}

// AudioData 音频数据
type AudioData struct {
	Samples    []float64
	SampleRate int
	Duration   time.Duration
}

// loadAudioData 加载音频数据
func loadAudioData(audioPath string) (*AudioData, error) {
	f, err := os.Open(audioPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := wav.NewDecoder(f)
	if !decoder.IsValidFile() {
		return nil, fmt.Errorf("不是有效的WAV文件")
	}

	buf, err := decoder.FullPCMBuffer()
	if err != nil {
		return nil, err
	}

	duration, err := decoder.Duration()
	if err != nil {
		return nil, err
	}

	data := &AudioData{
		SampleRate: int(decoder.SampleRate),
		Duration:   duration,
	}

	// 将音频数据归一化为[-1, 1]范围
	numSamples := buf.NumFrames() * buf.Format.NumChannels
	data.Samples = make([]float64, numSamples)

	// 简化音频处理 - 直接使用整数缓冲区
	intBuf := buf.AsIntBuffer()
	for i, s := range intBuf.Data {
		// 归一化到[-1, 1]范围
		data.Samples[i] = float64(s) / 32768.0
	}

	return data, nil
}

// calculateVolumeAtTime 计算指定时间的音量
func calculateVolumeAtTime(audioData *AudioData, t time.Duration) float64 {
	startSample := int(float64(t.Seconds()) * float64(audioData.SampleRate))
	endSample := startSample + audioData.SampleRate/frameRate

	if startSample < 0 {
		startSample = 0
	}
	if endSample > len(audioData.Samples) {
		endSample = len(audioData.Samples)
	}

	// 计算RMS音量
	var sumSquares float64
	for i := startSample; i < endSample; i++ {
		sumSquares += audioData.Samples[i] * audioData.Samples[i]
	}

	rms := math.Sqrt(sumSquares / float64(endSample-startSample))
	return rms
}

// mapVolumeToLipOpenness 将音量映射到嘴部开合程度
func mapVolumeToLipOpenness(volume float64) float64 {
	// 非线性映射：低音量时变化较小，高音量时变化较大
	openness := math.Pow(volume, 0.5) * 1.5
	if openness > 1.0 {
		openness = 1.0
	}
	if openness < 0.1 {
		openness = 0.1
	}
	return openness
}

// loadImage 加载图片
func loadImage(path string) (image.Image, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	img, _, err := image.Decode(f)
	if err != nil {
		return nil, err
	}

	// 调整图片大小
	img = imaging.Resize(img, outputWidth, outputHeight, imaging.Lanczos)
	return img, nil
}

// saveImage 保存图片
func saveImage(path string, img image.Image) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()

	switch strings.ToLower(filepath.Ext(path)) {
	case ".jpg", ".jpeg":
		return jpeg.Encode(f, img, &jpeg.Options{Quality: 90})
	case ".png":
		return png.Encode(f, img)
	default:
		return fmt.Errorf("不支持的图片格式: %s", filepath.Ext(path))
	}
}

// createVideoFromFrames 从帧创建视频
func createVideoFromFrames(framePaths []string, audioPath, outputPath string, fps int) error {
	// 1. 创建帧列表文件
	cw, _ := os.Getwd()
	listFilepath := filepath.Join(cw, "frame_list.txt")
	//listFile, err := os.CreateTemp("", "frame_list_*.txt")
	os.Remove(listFilepath)
	listFile, err := os.Create(listFilepath)
	if err != nil {
		return err
	}
	//defer os.Remove(listFile.Name())

	for _, path := range framePaths {
		if _, err := listFile.WriteString(fmt.Sprintf("file '%s'\n", path)); err != nil {
			return err
		}
	}
	listFile.Close()

	// 2. 使用FFmpeg创建视频
	fmt.Printf("开始用[%s]合成视频\r\n", listFilepath)
	cmd := exec.Command("ffmpeg",
		"-f", "concat",
		"-safe", "0",
		"-i", listFile.Name(),
		"-i", audioPath,
		"-c:v", "libx264",
		"-c:a", "aac",
		"-r", fmt.Sprintf("%d", fps),
		"-pix_fmt", "yuv420p",
		"-y", // 覆盖输出文件
		outputPath,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg执行失败: %v\n输出: %s", err, output)
	}

	return nil
}

// addQRCodeWatermark 添加二维码水印
func addQRCodeWatermark(img image.Image, text string) image.Image {
	// 生成二维码
	qrCode, err := generateQRCode(text, 100, 100)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		return img
	}

	// 定位在右下角
	pos := image.Point{
		X: img.Bounds().Dx() - qrCode.Bounds().Dx() - 10,
		Y: img.Bounds().Dy() - qrCode.Bounds().Dy() - 10,
	}

	return imaging.Overlay(img, qrCode, pos, 0.8)
}

// generateQRCode 生成二维码
func generateQRCode(text string, width, height int) (image.Image, error) {
	encoder := qrcode.NewQRCodeWriter()
	encodeHints := map[gozxing.EncodeHintType]interface{}{
		gozxing.EncodeHintType_ERROR_CORRECTION: "M",
	}

	bitMatrix, err := encoder.Encode(text, gozxing.BarcodeFormat_QR_CODE, width, height, encodeHints)
	if err != nil {
		return nil, err
	}

	img := image.NewRGBA(image.Rect(0, 0, width, height))
	white := color.RGBA{255, 255, 255, 255}
	black := color.RGBA{0, 0, 0, 255}

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, black)
			} else {
				img.Set(x, y, white)
			}
		}
	}

	return img, nil
}

// GenerateGIFPreview 生成GIF预览
func GenerateGIFPreview(inputImagePath, audioPath, outputGIFPath string) error {
	// 加载图片
	img, err := loadImage(inputImagePath)
	if err != nil {
		return fmt.Errorf("加载图片失败: %v", err)
	}

	// 创建动画器
	animator, err := NewLipAnimator("facefinder")
	if err != nil {
		return fmt.Errorf("创建动画器失败: %v", err)
	}

	// 检测人脸
	dets, err := animator.faceDetector.DetectFaces(img)
	if err != nil || len(dets) == 0 {
		return fmt.Errorf("人脸检测失败")
	}

	// 分析音频
	audioData, err := loadAudioData(audioPath)
	if err != nil {
		return fmt.Errorf("加载音频失败: %v", err)
	}

	// 生成GIF
	totalFrames := 30 // 预览只生成30帧
	delay := 100      // 每帧延迟(毫秒)
	g := &gif.GIF{}

	det := dets[0]
	mouthRect := estimateMouthRegion(det, img.Bounds().Dx(), img.Bounds().Dy())

	for i := 0; i < totalFrames; i++ {
		progress := float64(i) / float64(totalFrames)
		timeOffset := time.Duration(progress * float64(audioData.Duration))

		volume := calculateVolumeAtTime(audioData, timeOffset)
		openness := mapVolumeToLipOpenness(volume)

		frame := generateFrame(img, det, mouthRect, openness)
		paletted := image.NewPaletted(frame.Bounds(), nil)
		draw.Draw(paletted, paletted.Rect, frame, image.Point{}, draw.Src)

		g.Image = append(g.Image, paletted)
		g.Delay = append(g.Delay, delay)
	}

	// 保存GIF
	f, err := os.Create(outputGIFPath)
	if err != nil {
		return err
	}
	defer f.Close()

	return gif.EncodeAll(f, g)
}

func main() {
	fmt.Println("deepseek_video - 人脸动画生成器")

	// 检查级联文件是否存在
	cascadeFile := "facefinder"
	if _, err := os.Stat(cascadeFile); os.IsNotExist(err) {
		fmt.Printf("错误: 找不到人脸检测级联文件 '%s'\n", cascadeFile)
		fmt.Println("\n要获取此文件，请执行以下步骤:")
		fmt.Println("1. 访问 https://github.com/esimov/pigo")
		fmt.Println("2. 下载 cascade/facefinder 文件")
		fmt.Println("3. 将文件放在当前目录下")
		fmt.Println("\n或者运行以下命令下载:")
		fmt.Println("curl -O https://raw.githubusercontent.com/esimov/pigo/master/cascade/facefinder")
		fmt.Println("\n请先获取级联文件后再运行程序。")
		return
	}

	// 示例用法
	animator, err := NewLipAnimator(cascadeFile)
	if err != nil {
		log.Fatalf("初始化失败: %v", err)
	}

	// 检查输入文件
	if _, err := os.Stat("input.jpg"); os.IsNotExist(err) {
		fmt.Println("警告: 找不到输入图片 'input.jpg'")
		fmt.Println("请准备一张包含人脸的图片并命名为 'input.jpg'")
	}

	if _, err := os.Stat("speech.wav"); os.IsNotExist(err) {
		fmt.Println("警告: 找不到音频文件 'speech.wav'")
		fmt.Println("请准备一个WAV格式的音频文件并命名为 'speech.wav'")
	}

	// 如果文件存在，则生成动画
	if _, err1 := os.Stat("input.jpg"); err1 == nil {
		if _, err2 := os.Stat("speech.wav"); err2 == nil {
			fmt.Println("开始生成动画...")

			// 生成完整视频
			err = animator.GenerateAnimation("input.jpg", "speech.wav", "output.mp4")
			if err != nil {
				log.Printf("生成视频失败: %v", err)
			} else {
				fmt.Println("视频生成成功: output.mp4")
			}

			// 生成GIF预览
			err = GenerateGIFPreview("input.jpg", "speech.wav", "preview.gif")
			if err != nil {
				log.Printf("生成GIF预览失败: %v", err)
			} else {
				fmt.Println("GIF预览已生成: preview.gif")
			}
		}
	}
}
