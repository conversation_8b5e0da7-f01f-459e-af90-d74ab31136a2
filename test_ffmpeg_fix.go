package main

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

// 创建测试帧
func createTestFrame(frameIndex, totalFrames int, width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	
	// 创建动态背景
	progress := float64(frameIndex) / float64(totalFrames)
	
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			// 创建波浪效果
			wave := math.Sin(progress*4*math.Pi + float64(x)*0.02) * 0.5 + 0.5
			
			r := uint8(wave * 255)
			g := uint8(float64(y) / float64(height) * 255)
			b := uint8(float64(x) / float64(width) * 255)
			
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
	
	// 添加帧号文本区域
	textY := height / 2
	textHeight := 50
	for y := textY - textHeight/2; y < textY + textHeight/2; y++ {
		for x := width/4; x < 3*width/4; x++ {
			if x >= 0 && x < width && y >= 0 && y < height {
				img.Set(x, y, color.RGBA{255, 255, 255, 200})
			}
		}
	}
	
	return img
}

// 修复后的FFmpeg视频创建函数
func createVideoFromFrames(framePaths []string, outputPath string, fps int) error {
	if len(framePaths) == 0 {
		return fmt.Errorf("没有帧文件")
	}

	// 1. 创建帧列表文件
	cwd, err := os.Getwd()
	if err != nil {
		return err
	}
	
	listFilePath := filepath.Join(cwd, "frame_list.txt")
	os.Remove(listFilePath)
	
	listFile, err := os.Create(listFilePath)
	if err != nil {
		return err
	}
	defer listFile.Close()
	defer os.Remove(listFilePath)

	// 写入帧文件路径 - 使用绝对路径
	for _, path := range framePaths {
		absPath, err := filepath.Abs(path)
		if err != nil {
			return err
		}
		absPath = filepath.ToSlash(absPath)
		if _, err := listFile.WriteString(fmt.Sprintf("file '%s'\n", absPath)); err != nil {
			return err
		}
	}
	listFile.Close()

	// 2. 构建FFmpeg命令
	fmt.Printf("开始合成视频，共%d帧\n", len(framePaths))
	
	cmd := exec.Command("ffmpeg",
		"-f", "concat",
		"-safe", "0",
		"-i", listFilePath,
		"-c:v", "libx264",
		"-r", fmt.Sprintf("%d", fps),
		"-pix_fmt", "yuv420p",
		"-t", "3", // 限制为3秒测试视频
		"-y",
		outputPath,
	)

	fmt.Println("FFmpeg命令:", cmd.String())
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		fmt.Printf("FFmpeg错误:\n%s\n", string(output))
		return fmt.Errorf("ffmpeg执行失败: %v", err)
	}
	
	fmt.Printf("视频创建成功: %s\n", outputPath)
	return nil
}

func main() {
	fmt.Println("测试FFmpeg修复...")
	
	// 创建测试目录
	frameDir := "test_frames"
	os.RemoveAll(frameDir)
	os.MkdirAll(frameDir, 0755)
	defer os.RemoveAll(frameDir)
	
	// 生成测试帧
	fps := 24
	duration := 3 // 3秒
	totalFrames := fps * duration
	framePaths := make([]string, totalFrames)
	
	fmt.Printf("生成%d个测试帧...\n", totalFrames)
	for i := 0; i < totalFrames; i++ {
		frame := createTestFrame(i, totalFrames, 640, 480)
		framePath := filepath.Join(frameDir, fmt.Sprintf("frame_%04d.png", i))
		
		f, err := os.Create(framePath)
		if err != nil {
			fmt.Printf("创建帧文件失败: %v\n", err)
			return
		}
		
		if err := png.Encode(f, frame); err != nil {
			f.Close()
			fmt.Printf("保存帧失败: %v\n", err)
			return
		}
		f.Close()
		
		framePaths[i] = framePath
		
		if (i+1)%24 == 0 {
			fmt.Printf("已生成 %d/%d 帧\n", i+1, totalFrames)
		}
	}
	
	// 测试视频创建
	outputPath := "test_output_fixed.mp4"
	fmt.Println("\n开始测试视频合成...")
	
	start := time.Now()
	if err := createVideoFromFrames(framePaths, outputPath, fps); err != nil {
		fmt.Printf("视频创建失败: %v\n", err)
		return
	}
	
	elapsed := time.Since(start)
	fmt.Printf("视频创建完成，耗时: %v\n", elapsed)
	
	// 检查输出文件
	if info, err := os.Stat(outputPath); err == nil {
		fmt.Printf("输出文件: %s (大小: %d 字节)\n", outputPath, info.Size())
		fmt.Println("✅ FFmpeg修复测试成功！")
		fmt.Println("\n可以播放 test_output_fixed.mp4 查看结果")
	} else {
		fmt.Printf("❌ 输出文件不存在: %v\n", err)
	}
}
