﻿package main

import (
"fmt"
"log"
"os"
)

func main() {
fmt.Println("测试 Go 环境...")

// 检查必要的文件
files := []string{"input.jpg", "speech.wav", "facefinder"}
missing := []string{}

for _, file := range files {
if _, err := os.Stat(file); os.IsNotExist(err) {
missing = append(missing, file)
}
}

if len(missing) > 0 {
fmt.Printf("缺少以下文件: %v\n", missing)
fmt.Println("请准备这些文件后再运行完整程序")
} else {
fmt.Println("所有必要文件都存在，可以运行完整程序")
}

log.Println("Go 模块环境正常工作！")
}
