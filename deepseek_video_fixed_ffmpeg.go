package main

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"log"
	"math"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	pigo "github.com/esimov/pigo/core"
	"github.com/go-audio/wav"
	"github.com/makiuchi-d/gozxing"
	"github.com/makiuchi-d/gozxing/qrcode"
)

const (
	outputWidth  = 640
	outputHeight = 480
	frameRate    = 24
)

// FaceDetector 人脸检测器
type FaceDetector struct {
	cascadeFile string
	classifier  *pigo.Pigo
}

// NewFaceDetector 创建人脸检测器
func NewFaceDetector(cascadeFile string) (*FaceDetector, error) {
	cascade, err := os.ReadFile(cascadeFile)
	if err != nil {
		return nil, fmt.Errorf("读取级联文件失败: %v", err)
	}

	p := pigo.NewPigo()
	classifier, err := p.Unpack(cascade)
	if err != nil {
		return nil, fmt.Errorf("解析级联文件失败: %v", err)
	}

	return &FaceDetector{
		cascadeFile: cascadeFile,
		classifier:  classifier,
	}, nil
}

// DetectFaces 检测图片中的人脸
func (fd *FaceDetector) DetectFaces(img image.Image) ([]pigo.Detection, error) {
	src := imaging.Clone(img)
	gray := imaging.Grayscale(src)
	pixels := pigo.RgbToGrayscale(gray)
	cols, rows := src.Bounds().Dx(), src.Bounds().Dy()

	cParams := pigo.CascadeParams{
		MinSize:     100,
		MaxSize:     600,
		ShiftFactor: 0.1,
		ScaleFactor: 1.1,
		ImageParams: pigo.ImageParams{
			Pixels: pixels,
			Rows:   rows,
			Cols:   cols,
			Dim:    cols,
		},
	}

	dets := fd.classifier.RunCascade(cParams, 0.0)
	dets = fd.classifier.ClusterDetections(dets, 0.2)

	return dets, nil
}

// LipAnimator 嘴部动画生成器
type LipAnimator struct {
	faceDetector *FaceDetector
}

// NewLipAnimator 创建嘴部动画生成器
func NewLipAnimator(cascadeFile string) (*LipAnimator, error) {
	fd, err := NewFaceDetector(cascadeFile)
	if err != nil {
		return nil, err
	}
	return &LipAnimator{faceDetector: fd}, nil
}

// AudioData 音频数据
type AudioData struct {
	Samples    []float64
	SampleRate int
	Duration   time.Duration
}

// loadAudioData 加载音频数据
func loadAudioData(audioPath string) (*AudioData, error) {
	f, err := os.Open(audioPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := wav.NewDecoder(f)
	if !decoder.IsValidFile() {
		return nil, fmt.Errorf("不是有效的WAV文件")
	}

	buf, err := decoder.FullPCMBuffer()
	if err != nil {
		return nil, err
	}

	duration, err := decoder.Duration()
	if err != nil {
		return nil, err
	}

	data := &AudioData{
		SampleRate: int(decoder.SampleRate),
		Duration:   duration,
	}

	// 简化音频处理 - 直接使用整数缓冲区
	numSamples := buf.NumFrames() * buf.Format.NumChannels
	data.Samples = make([]float64, numSamples)

	intBuf := buf.AsIntBuffer()
	for i, s := range intBuf.Data {
		// 归一化到[-1, 1]范围
		data.Samples[i] = float64(s) / 32768.0
	}

	return data, nil
}

// calculateVolumeAtTime 计算指定时间的音量
func calculateVolumeAtTime(audioData *AudioData, t time.Duration) float64 {
	startSample := int(float64(t.Seconds()) * float64(audioData.SampleRate))
	endSample := startSample + audioData.SampleRate/frameRate

	if startSample < 0 {
		startSample = 0
	}
	if endSample > len(audioData.Samples) {
		endSample = len(audioData.Samples)
	}
	if startSample >= endSample {
		return 0.0
	}

	// 计算RMS音量
	var sumSquares float64
	for i := startSample; i < endSample; i++ {
		sumSquares += audioData.Samples[i] * audioData.Samples[i]
	}

	rms := math.Sqrt(sumSquares / float64(endSample-startSample))
	return rms
}

// mapVolumeToLipOpenness 将音量映射到嘴部开合程度
func mapVolumeToLipOpenness(volume float64) float64 {
	// 非线性映射：低音量时变化较小，高音量时变化较大
	openness := math.Pow(volume, 0.5) * 1.5
	if openness > 1.0 {
		openness = 1.0
	}
	if openness < 0.1 {
		openness = 0.1
	}
	return openness
}

// estimateMouthRegion 估计嘴部区域
func estimateMouthRegion(face pigo.Detection, imgWidth, imgHeight int) image.Rectangle {
	x := int(float64(face.Col)) - int(float64(face.Scale)/2)
	y := int(float64(face.Row)) - int(float64(face.Scale)/2)

	// 嘴部位于人脸下半部分
	mouthY := y + int(float64(face.Scale)*0.6)
	mouthHeight := int(float64(face.Scale) * 0.2)
	mouthWidth := int(float64(face.Scale) * 0.4)

	return image.Rect(
		x+int(float64(face.Scale)*0.3),
		mouthY,
		x+int(float64(face.Scale)*0.3)+mouthWidth,
		mouthY+mouthHeight,
	)
}

// loadImage 加载图片
func loadImage(path string) (image.Image, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	img, _, err := image.Decode(f)
	if err != nil {
		return nil, err
	}

	// 调整图片大小
	img = imaging.Resize(img, outputWidth, outputHeight, imaging.Lanczos)
	return img, nil
}

// saveImage 保存图片
func saveImage(path string, img image.Image) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()

	switch strings.ToLower(filepath.Ext(path)) {
	case ".jpg", ".jpeg":
		return jpeg.Encode(f, img, &jpeg.Options{Quality: 90})
	case ".png":
		return png.Encode(f, img)
	default:
		return fmt.Errorf("不支持的图片格式: %s", filepath.Ext(path))
	}
}

// createMouthImage 创建嘴部图像
func createMouthImage(width, height int, openness float64) image.Image {
	// 创建透明背景
	mouthImg := image.NewRGBA(image.Rect(0, 0, width, height))
	draw.Draw(mouthImg, mouthImg.Bounds(), image.Transparent, image.Point{}, draw.Src)

	// 绘制嘴部
	lipColor := color.RGBA{200, 100, 100, 255}
	lipThickness := 3
	centerY := height / 2

	// 上嘴唇
	upperLipHeight := int(float64(height/3) * openness)
	for y := centerY - upperLipHeight; y < centerY; y++ {
		for x := 0; x < width; x++ {
			// 绘制曲线形状
			if math.Abs(float64(x-width/2)) < float64(width/2)*math.Sin(math.Pi*float64(y-centerY+upperLipHeight)/float64(upperLipHeight)) {
				for t := 0; t < lipThickness; t++ {
					if y+t < centerY {
						mouthImg.Set(x, y+t, lipColor)
					}
				}
			}
		}
	}

	// 下嘴唇
	lowerLipHeight := int(float64(height/4) * openness)
	for y := centerY; y < centerY+lowerLipHeight; y++ {
		for x := 0; x < width; x++ {
			// 绘制曲线形状
			if math.Abs(float64(x-width/2)) < float64(width/2)*math.Sin(math.Pi*float64(y-centerY)/float64(lowerLipHeight)) {
				for t := 0; t < lipThickness; t++ {
					if y-t >= centerY {
						mouthImg.Set(x, y-t, lipColor)
					}
				}
			}
		}
	}

	// 口腔内部 (当嘴张开时)
	if openness > 0.3 {
		mouthOpening := int(float64(height) * openness * 0.8)
		innerMouth := image.Rect(width/4, centerY-mouthOpening/2, 3*width/4, centerY+mouthOpening/2)
		draw.Draw(mouthImg, innerMouth, &image.Uniform{color.RGBA{50, 20, 20, 255}}, image.Point{}, draw.Src)
	}

	return mouthImg
}

// generateQRCode 生成二维码
func generateQRCode(text string, width, height int) (image.Image, error) {
	encoder := qrcode.NewQRCodeWriter()
	encodeHints := map[gozxing.EncodeHintType]interface{}{
		gozxing.EncodeHintType_ERROR_CORRECTION: "M",
	}

	bitMatrix, err := encoder.Encode(text, gozxing.BarcodeFormat_QR_CODE, width, height, encodeHints)
	if err != nil {
		return nil, err
	}

	img := image.NewRGBA(image.Rect(0, 0, width, height))
	white := color.RGBA{255, 255, 255, 255}
	black := color.RGBA{0, 0, 0, 255}

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, black)
			} else {
				img.Set(x, y, white)
			}
		}
	}

	return img, nil
}

// addQRCodeWatermark 添加二维码水印
func addQRCodeWatermark(img image.Image, text string) image.Image {
	// 生成二维码
	qrCode, err := generateQRCode(text, 100, 100)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		return img
	}

	// 定位在右下角
	pos := image.Point{
		X: img.Bounds().Dx() - qrCode.Bounds().Dx() - 10,
		Y: img.Bounds().Dy() - qrCode.Bounds().Dy() - 10,
	}

	return imaging.Overlay(img, qrCode, pos, 0.8)
}

// generateFrame 生成单个动画帧
func generateFrame(baseImg image.Image, face pigo.Detection, mouthRect image.Rectangle, openness float64) image.Image {
	// 创建新图像
	img := imaging.Clone(baseImg)

	// 调整嘴部区域
	mouthImg := createMouthImage(mouthRect.Dx(), mouthRect.Dy(), openness)

	// 将嘴部图像应用到原图
	img = imaging.Overlay(img, mouthImg, mouthRect.Min, 1.0)

	// 添加二维码水印
	finalImg := addQRCodeWatermark(img, "Generated by Go Lip Animator")

	return finalImg
}
