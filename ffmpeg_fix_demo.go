package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

// 修复后的FFmpeg视频创建函数
func createVideoFromFramesFixed(framePaths []string, audioPath, outputPath string, fps int) error {
	if len(framePaths) == 0 {
		return fmt.Errorf("没有帧文件")
	}

	// 1. 创建帧列表文件 - 使用绝对路径
	cwd, err := os.Getwd()
	if err != nil {
		return err
	}
	
	listFilePath := filepath.Join(cwd, "frame_list.txt")
	
	// 删除旧的列表文件
	os.Remove(listFilePath)
	
	listFile, err := os.Create(listFilePath)
	if err != nil {
		return err
	}
	defer listFile.Close()
	defer os.Remove(listFilePath) // 清理临时文件

	// 写入帧文件路径 - 确保使用绝对路径
	for _, path := range framePaths {
		absPath, err := filepath.Abs(path)
		if err != nil {
			return err
		}
		// 在Windows上需要转换路径分隔符
		absPath = filepath.ToSlash(absPath)
		if _, err := listFile.WriteString(fmt.Sprintf("file '%s'\n", absPath)); err != nil {
			return err
		}
	}
	listFile.Close()

	// 2. 检查音频文件是否存在
	if _, err := os.Stat(audioPath); os.IsNotExist(err) {
		return fmt.Errorf("音频文件不存在: %s", audioPath)
	}

	// 3. 构建FFmpeg命令 - 修复后的版本
	fmt.Printf("开始用[%s]合成视频，共%d帧\n", listFilePath, len(framePaths))
	
	cmd := exec.Command("ffmpeg",
		"-f", "concat",           // 使用concat格式
		"-safe", "0",             // 允许不安全的文件路径
		"-i", listFilePath,       // 输入帧列表文件
		"-i", audioPath,          // 输入音频文件
		"-c:v", "libx264",        // 视频编码器
		"-c:a", "aac",            // 音频编码器
		"-r", fmt.Sprintf("%d", fps), // 设置帧率
		"-pix_fmt", "yuv420p",    // 像素格式（兼容性好）
		"-shortest",              // 以最短的流为准（重要！）
		"-y",                     // 覆盖输出文件
		outputPath,               // 输出文件
	)

	// 4. 执行命令并获取详细输出
	fmt.Println("FFmpeg命令:", cmd.String())
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		fmt.Printf("FFmpeg错误输出:\n%s\n", string(output))
		return fmt.Errorf("ffmpeg执行失败: %v", err)
	}
	
	fmt.Printf("FFmpeg成功输出:\n%s\n", string(output))
	return nil
}

// 原始有问题的FFmpeg函数（用于对比）
func createVideoFromFramesOriginal(framePaths []string, audioPath, outputPath string, fps int) error {
	// 问题1: 使用临时文件，可能被过早删除
	listFile, err := os.CreateTemp("", "frame_list_*.txt")
	if err != nil {
		return err
	}
	defer os.Remove(listFile.Name()) // 可能过早删除

	for _, path := range framePaths {
		// 问题2: 可能使用相对路径，FFmpeg找不到文件
		if _, err := listFile.WriteString(fmt.Sprintf("file '%s'\n", path)); err != nil {
			return err
		}
	}
	listFile.Close()

	// 问题3: 缺少 -shortest 参数，可能导致视频长度不匹配
	cmd := exec.Command("ffmpeg",
		"-f", "concat",
		"-safe", "0",
		"-i", listFile.Name(), // 问题4: 文件可能已被删除
		"-i", audioPath,
		"-c:v", "libx264",
		"-c:a", "aac",
		"-r", fmt.Sprintf("%d", fps),
		"-pix_fmt", "yuv420p",
		"-y",
		outputPath,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg执行失败: %v\n输出: %s", err, output)
	}

	return nil
}

func main() {
	fmt.Println("FFmpeg修复演示")
	fmt.Println("================")
	
	fmt.Println("\n🔧 主要修复点:")
	fmt.Println("1. 使用绝对路径而不是相对路径")
	fmt.Println("2. 添加 -shortest 参数确保视频长度与音频匹配")
	fmt.Println("3. 改进文件路径处理，兼容Windows")
	fmt.Println("4. 更好的错误处理和调试输出")
	fmt.Println("5. 确保临时文件在FFmpeg执行完成后才删除")
	
	fmt.Println("\n📋 修复后的FFmpeg命令模板:")
	fmt.Println("ffmpeg -f concat -safe 0 -i frame_list.txt -i audio.wav \\")
	fmt.Println("       -c:v libx264 -c:a aac -r 24 -pix_fmt yuv420p \\")
	fmt.Println("       -shortest -y output.mp4")
	
	fmt.Println("\n💡 关键参数说明:")
	fmt.Println("- -shortest: 确保输出视频长度与最短输入流匹配")
	fmt.Println("- -safe 0: 允许使用绝对路径")
	fmt.Println("- -pix_fmt yuv420p: 确保兼容性")
	fmt.Println("- 绝对路径: 避免FFmpeg找不到文件")
}
