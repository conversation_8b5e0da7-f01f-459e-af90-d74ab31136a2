package main

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"log"
	"os"

	"github.com/disintegration/imaging"
	"github.com/makiuchi-d/gozxing"
	"github.com/makiuchi-d/gozxing/qrcode"
)

// generateQRCode 生成二维码
func generateQRCode(text string, width, height int) (image.Image, error) {
	encoder := qrcode.NewQRCodeWriter()
	encodeHints := map[gozxing.EncodeHintType]interface{}{
		gozxing.EncodeHintType_ERROR_CORRECTION: "M",
	}

	bitMatrix, err := encoder.Encode(text, gozxing.BarcodeFormat_QR_CODE, width, height, encodeHints)
	if err != nil {
		return nil, err
	}

	img := image.NewRGBA(image.Rect(0, 0, width, height))
	white := color.RGBA{255, 255, 255, 255}
	black := color.RGBA{0, 0, 0, 255}

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, black)
			} else {
				img.Set(x, y, white)
			}
		}
	}

	return img, nil
}

// addQRCodeWatermark 添加二维码水印
func addQRCodeWatermark(img image.Image, text string) image.Image {
	// 生成二维码
	qrCode, err := generateQRCode(text, 100, 100)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		return img
	}

	// 定位在右下角
	pos := image.Point{
		X: img.Bounds().Dx() - qrCode.Bounds().Dx() - 10,
		Y: img.Bounds().Dy() - qrCode.Bounds().Dy() - 10,
	}

	return imaging.Overlay(img, qrCode, pos, 0.8)
}

func main() {
	fmt.Println("测试编译修复...")

	// 创建一个简单的测试图像
	img := image.NewRGBA(image.Rect(0, 0, 640, 480))

	// 填充背景色
	for y := 0; y < 480; y++ {
		for x := 0; x < 640; x++ {
			img.Set(x, y, color.RGBA{100, 150, 200, 255})
		}
	}

	// 测试图像处理
	resized := imaging.Resize(img, 320, 240, imaging.Lanczos)
	fmt.Printf("图像调整大小成功: %dx%d -> %dx%d\n",
		img.Bounds().Dx(), img.Bounds().Dy(),
		resized.Bounds().Dx(), resized.Bounds().Dy())

	// 测试二维码生成
	_, err := generateQRCode("Test QR Code", 100, 100)
	if err != nil {
		log.Fatalf("二维码生成失败: %v", err)
	}
	fmt.Println("二维码生成成功")

	// 测试水印添加
	watermarked := addQRCodeWatermark(resized, "Test Watermark")
	fmt.Println("水印添加成功")

	// 保存测试图像
	f, err := os.Create("test_output.png")
	if err != nil {
		log.Fatalf("创建文件失败: %v", err)
	}
	defer f.Close()

	if err := png.Encode(f, watermarked); err != nil {
		log.Fatalf("保存图像失败: %v", err)
	}

	fmt.Println("测试完成！所有依赖库工作正常。")
	fmt.Println("测试图像已保存为: test_output.png")
}
